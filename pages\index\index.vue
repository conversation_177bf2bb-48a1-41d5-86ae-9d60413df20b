<template>
	<view class="pl-4 py-4 width-100 box-border mheight-100vh bg-light">
		<view v-for="(item,index) in list" :key='index' class=" pr-4 box-border span-10 d-inline-block mb-4" @tap="onOpencard(item)">
			<view class="width-100 img-box rounded-12  overflow-hidden shadow-md">
				<view class="position-absolute top-0 right-0 bottom-0 left-0 ">
					<image :src="item.picture" mode="aspectFill" class="width-100 height-100"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {ref} from "vue"
	// import {onShow} from "@dcloudio/uni-app"
	import  mockJson from "@/api/mock-card.json"
	
	const list=ref(mockJson)
	// onShow(()=>{
	// 	uni.showLoading({
	// 		mask:true,
	// 	})
	// 	setTimeout(()=>{
	// 		uni.hideLoading()
	// 	},1000)
	// })
	function onOpencard(item){
		uni.navigateTo({
			url:`/pages/cardDetail/cardDetail?id=${item.id}`
		})
	}
</script>

<style lang="scss" scoped>
	.img-box{
		height: 0;
		padding-bottom:133.33%;
		position: relative;
	}
</style>