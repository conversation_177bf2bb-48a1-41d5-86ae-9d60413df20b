<template>
	<view  class="tc-wrap position-fixed zindex-999 left-0 top-0 right-0 bottom -0 flex flex-column height-100vh overflow-y-auto justify-center align-center p-4" @click="onClose">
		<view class="main-wrap bg-white position-relative width-100 rounded-8   p-4 box-border" :class="hasRoundIcon ? 'roundIcon':''"  @click.stop>
			<template v-if='customTitle'>
				<view class="flex justify-between align-center border-bottom pb-4">
					<view class="flex-1">
						<slot name="titleLeft"></slot>
					</view>
					<view class="close-box  flex-shrink" @click='onClose'>
						<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
					</view>
				</view>
			</template>
			<template v-else>
				<view class="close-box isRound position-absolute flex-shrink" @click='onClose'>
					<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
				</view>
			</template>
			<view v-if="hasRoundIcon" class="round-icon-wrap mb-5">
				<view class="bg-theme-brand rounded-circle width-100 height-100 flex align-center justify-center p-3 box-border">
					<image :src="roundIcon" mode="aspectFit" class="width-100 height-100"></image>
				</view>
			</view>
			<view v-if="!customTitle" class="mb-4 pt-1 text-center">
				<slot name="title">
					<view class="font-weight-bold text-first font-20 my-0-8">
					  {{title}}
				    </view>
				</slot>
				<slot name="desc">
					<view class="text-second font-14">
						{{desc}}
					</view>
				</slot>
			</view>
			<slot></slot>
		</view>
	</view>
</template>

<script setup>
	import { defineProps,defineEmits,computed } from 'vue';
	const props=defineProps({
		roundIcon:{
			type:String,
			default:''
		}, // 是否弹窗中间显示圆角图标
		title:{
			type:String,
			default:""
		}, // 标题
		desc:{
			type:String,
			default:""
		}, // 标题描述
		customTitle:{
			type:Boolean,
			default:false
		}, // 自定义标题
	})
	const emits=defineEmits(['close'])
	const hasRoundIcon=!!props.roundIcon
	function onClose() {
		emits("close")
	}
</script>

<style lang="scss" scoped>
	$roundIconWidth:176rpx;
.tc-wrap{
	background-color: rgba(#12131B,0.5);
	.close-box{
		width:32rpx;
		height: 32rpx;
		&.isRound{
			right:40rpx;
			top:40rpx;
		}
	}
	.main-wrap{
		&.roundIcon{
			padding-top:calc($roundIconWidth / 2)
		}
	}
}
.round-icon-wrap{
	width: $roundIconWidth;
	height:$roundIconWidth;
	border-radius: 100%;
	background-color: inherit;
	position: absolute;
	left:50%;
	top:0;
	transform: translateX(-50%) translateY(-50%);
	box-sizing: border-box;
	padding: 16rpx;
	// margin-bottom: 80rpx;
}
</style>