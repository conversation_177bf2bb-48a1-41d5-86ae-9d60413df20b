/* 主色调 */ 
$primary:#2F56E8;  /*蓝色*/
$primary-muted:mix(#000,$primary,30%);  
$primary-light:lighten($primary, 10%);  
$themeBrand:#202143; /*主题色*/

$success:#83B24C;  /*绿色*/
$success-muted:darken($success,20%);  /*绿色*/
$success-light:lighten($success,20%);  /*绿色*/
$danger:#E2483C;   /*红色*/
$danger-muted:darken($danger,20%);   /*红色*/
$danger-light:lighten($danger,20%);   /*红色*/
$warning:#ECB332; /*黄色*/
$warning-muted:darken($warning,20%); /*黄色*/
$warning-light:lighten($warning,20%); /*黄色*/
$info:#F1F3F5;  /*详情*/
$light:#EBEDEE;   /*跟白色无差的灰色,用于背景*/
$light-detail:#F3F4F8;   /*跟白色无差的灰色,用于背景*/
$light-muted:mix(#000,$light,10%);   

/*字体色调*/
$first:#092119;  /*字体高强调*/
$second:#646A73;  /*灰色*/
$third:#969BA4; /*灰色 用于副标题 备注这种*/
$fourth:#b2b2b2; /*灰色 用于副标题 备注这种*/

/*常用颜色*/
$red:red; /*红色*/
$black:#000; /*黑色*/
$white:#fff;  /*白色*/
/*表单色调*/
$placeholder:#B6BAC1;  /*表单placeholder 的颜色*/
$colorInput:#808080;
$bgInput:#F3F4F8;

/*对应鼠标按下去的颜色*/
$hoverPrimary:#EBEBEB;
/*边框色调*/
$borderColor:#EBEDEE; /*边框浅灰色*/

$padbase: 24rpx;
$marbase: 24rpx;