import {ref,computed} from "vue"
import { defineStore } from 'pinia';
const _loginRes=uni.getStorageSync("loginRes") ? uni.getStorageSync("loginRes") : {}
export const useUserStore = defineStore('user',()=>{
	
	const loginRes=ref(_loginRes)
	const clientId=ref("e5cd7e4891bf95d1d19206ce24a7b32e")
	const grantType=ref("xcx")
	const tenantId=ref("000000")
	// const agentId=ref('1956257023227404289')  //  助手id
	
	
	const accessToken=computed(()=>{
		return loginRes.value.access_token
	})
	
	function saveLoginRes(obj){
		loginRes.value=obj
		uni.setStorageSync("loginRes",obj)
	}
	function clearStorage(){
		loginRes.value={}
		uni.removeStorageSync("loginRes")
	}
	// function saveAgentId(id=''){
	// 	agentId.value=id
	// }
	
	return {
		loginRes,
		clientId,
		grantType,
		tenantId,
		// agentId,
		accessToken,
		saveLoginRes,
		clearStorage,
	}
});