import { defineStore } from 'pinia';
const _loginRes=uni.getStorageSync("loginRes") ? uni.getStorageSync("loginRes") : {}
export const useUserStore = defineStore('user', {
	state: () => {
		return { 
			loginRes: _loginRes ,
			clientId:"e5cd7e4891bf95d1d19206ce24a7b32e",
			"grantType": "xcx",
			"tenantId": "000000",
			agentId:'1956257023227404289'
		};
	},
	getters: {
		accessToken:(state)=>state.loginRes.access_token
	},
	// 也可以这样定义
	// state: () => ({ count: 0 })
	actions: {
		saveLoginRes(obj){
			this.loginRes=obj
			uni.setStorageSync("loginRes",obj)
		},
		clearStorage(){
			this.loginRes={}
			uni.removeStorageSync("loginRes")
		}
	},
});