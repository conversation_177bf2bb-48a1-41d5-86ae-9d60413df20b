<template>
	<view class="content">
		<canvas type="2d" id="myCanvas" class="poster_canvas"></canvas>
		<view class="my-base"><button @click="onCreateAvatar">生成头像</button></view>
		<button open-type="share" class="bg-theme-brand text-white py-1 font-12 flex align-center justify-evenly rounded-8 card-box">
			分享名片
		</button>
		<view class="my-base">
			生成后的头像：
		</view>
		<image :src='avatar' mode="heightFix" class="width-100"></image>
	</view>
</template>

<script setup>
	import {ref} from "vue"
	import { onShareAppMessage, onLoad, onReady,onUnload } from '@dcloudio/uni-app';
	const avatar=ref("")  // 头像
	const  avatar_arrayBuffer=ref("") // 头像 数据
	//用户点击分享
	onShareAppMessage((e) => {
		let shareInfo = {
			path: `/pages/index/index?id=10`,
			title: `张三`,
			imageUrl: avatar.value
		};
		return shareInfo;
	});
	onLoad(()=>{
		formatFaceData()
		// onCreateAvatar()
	})
	// 函数
	//  人脸识别
	function formatFaceData() {
		const imagePath='/static/images/body.png'
		const fs = uni.getFileSystemManager();
		fs.readFile({
		      filePath: imagePath,
		      success:async (data)=>{
		        avatar_arrayBuffer.value = data.data;
				console.log("avatar_arrayBuffer",data.data)
				const imgInfo=await uni.getImageInfo({
					src:imagePath
				})
				console.log("imgInfo",imgInfo.height)
				// resolved: createVKSession:fail The current device does not support version "v1"(env: Windows,mp,1.06.2504010; lib: 3.9.2)
				if(imgInfo.errMsg.indexOf("ok")>=0){
					// 检查是否支持VK功能
					if (typeof wx.createVKSession === 'function') {
						try {
							const session = wx.createVKSession({
							   track: {
								 body: { mode: 2 }
							   },
							})
							session.on('updateAnchors', anchors => {
								 console.log("anchors",anchors)
							})
							session.start(errno=>{
								if(errno){
									console.log("VK session start error:",errno)
									console.log("VK功能在当前环境不支持，跳过人体识别")
								}else{
									const result=session.detectBody({
									  frameBuffer:avatar_arrayBuffer.value,
									  width:588,
									  height:453,
									  scoreThreshold: 0.8,
									  sourceType: 1,
									  // modelMode: 1,
									})
									console.log("Body detection result:",result)
								}
							})
						} catch (error) {
							console.log("VK session creation failed:", error)
							console.log("VK功能在当前环境不支持，跳过人体识别")
						}
					} else {
						console.log("VK API not available in current environment")
						console.log("VK功能在当前环境不支持，跳过人体识别")
					}
				}
		      },
		      fail(err) {
		        console.error('读取文件失败', err);
		    }
	    });
	}
	// 生成海报
	function onCreateAvatar() {
		const query = wx.createSelectorQuery()
		query.select('#myCanvas')
		  .fields({ node: true, size: true })
		  .exec((res) => {
			const canvas = res[0].node
			const ctx = canvas.getContext('2d')
	
			const dpr = wx.getSystemInfoSync().pixelRatio
			const width= res[0].width
			const height = res[0].height
			canvas.width=width*dpr
			canvas.height=height*dpr
			ctx.scale(dpr, dpr)
			const bgImage = canvas.createImage()
			const img2 = canvas.createImage()
			const avatarImg=canvas.createImage()
		
			ctx.imageSmoothingEnabled = true; 
			bgImage.src = '/static/images/<EMAIL>'
			bgImage.crossOrigin = 'anonymous'; 
			bgImage.onerror = function() {
				console.log('图片加载失败，请尝试其他图片');
			};
			bgImage.onload = (e) => {
				ctx.clearRect(0, 0, canvas.width, canvas.height);
				ctx.drawImage(
					bgImage,
					0,
					0,
					width,
					height,
				)
				avatarImg.src='/static/images/image.png'
				avatarImg.crossOrigin = 'anonymous';
				const avatarImgDx=width-30-182
				const avatarImgDy=24
				avatarImg.onload = (e) => {
					ctx.drawImage(bgImage, 0, 0, width,height);
					ctx.drawImage(
					    avatarImg,
						avatarImgDx,
						avatarImgDy,
						182,
						284
					)
					img2.src = "/static/images/<EMAIL>"
					img2.crossOrigin = 'anonymous';
					const img2H=width*107/500
					const img2dy=height-img2H
					img2.onload = (e) => {
						ctx.drawImage(
							img2,
							0,
							img2dy,
							width,
							img2H,
						)
						drawText(ctx)
						uni.canvasToTempFilePath({
						    canvas: canvas,
						    success(res) {
								avatar.value=res.tempFilePath
						    }
						})
					}
				}
			}
		})
		function drawText(ctx){
			 // 绘制名字
			ctx.font = `bold 36px 苹方-简 中粗体`;
			ctx.fillStyle = "#092119";
			ctx.textAlign = "left";
			ctx.textBaseline = 'middle';
			ctx.fillText("林薇", 53, 106);
			// 公司
			ctx.font = `18px 苹方-简 常规体`;
			ctx.fillStyle = "#092119";
			ctx.textAlign = "left";
			ctx.fillText("EaseInsur", 53, 160);
			ctx.fillText("高级销售经理", 53, 195);
		}
	}
	
</script>

<style scoped lang="scss">
	.poster_canvas{
		width: 750rpx;  // 750 *600
		height: 600rpx;
		// position: fixed;
		// top: -10000rpx;
		left: 0rpx;
		box-sizing: border-box;
	}
</style>
