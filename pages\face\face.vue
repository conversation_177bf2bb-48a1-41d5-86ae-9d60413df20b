<template>
	<view class="content">
		<!-- todo 原本这里会渲染对应图片，现在不渲染了，符合预期吗 -->
		<canvas type="2d" id="myCanvas" class="poster_canvas"></canvas>
		<button @click="onCreateAvatar">生成头像</button>
		<button @click="formatFaceData">测试人体检测</button>
		<button open-type="share">
			分享名片
		</button>

		<!-- 人体检测结果显示区域 -->
		<view class="detection-result">
			<view class="result-title">人体检测结果：</view>
			<view class="result-status" :class="detectionStatus">{{ statusText }}</view>
			<view v-if="bodyAnchors.length > 0" class="anchors-info">
				<view class="anchor-count">检测到 {{ bodyAnchors.length }} 个人体</view>
				<view v-for="(anchor, index) in bodyAnchors" :key="index" class="anchor-item">
					<view class="anchor-title">人体 {{ index + 1 }}:</view>
					<view class="anchor-detail">关键点数量: {{ anchor.points?.length || 0 }}</view>
					<view class="anchor-detail">置信度: {{ (anchor.score * 100).toFixed(1) }}%</view>
					<view class="anchor-detail">检测框: {{ anchor.size?.width }}x{{ anchor.size?.height }}</view>
				</view>
			</view>
		</view>

		<view class="my-base">
			生成后的头像：
		</view>
		<image :src='avatar' mode="heightFix" class="width-100"></image>
	</view>
</template>

<script setup>
	import {ref} from "vue"
	import { onShareAppMessage, onLoad, onReady,onUnload } from '@dcloudio/uni-app';
	const avatar=ref("")  // 头像
	const  avatar_arrayBuffer=ref("") // 头像 数据

	// 人体检测结果相关数据
	const bodyAnchors = ref([]) // 检测到的人体锚点
	const detectionStatus = ref('waiting') // 检测状态: waiting, detecting, success, failed
	const statusText = ref('等待检测...')
	//用户点击分享
	onShareAppMessage((e) => {
		let shareInfo = {
			path: `/pages/index/index?id=10`,
			title: `张三`,
			imageUrl: avatar.value
		};
		return shareInfo;
	});
	onLoad(()=>{
		onCreateAvatar() // 先生成头像
		// formatFaceData() // 暂时注释掉自动检测，改为手动点击
	})
	// 函数
	//  人体识别处理
	async function formatFaceData() {
		try {
			// 更新状态
			detectionStatus.value = 'detecting'
			statusText.value = '正在检测...'
			bodyAnchors.value = []

			const imagePath = '/static/images/body-w200.png'
			console.log("Starting body detection for:", imagePath)

			// 检查VK API是否可用
			if (typeof wx.createVKSession !== 'function') {
				console.log("VK API not available, skipping body detection")
				detectionStatus.value = 'failed'
				statusText.value = 'VK API 不可用'
				return
			}

			console.log("VK API is available, proceeding with detection")

			// 获取图片信息
			const imgInfo = await getImageInfo(imagePath)
			if (!imgInfo) {
				console.error("Failed to get image info")
				detectionStatus.value = 'failed'
				statusText.value = '获取图片信息失败'
				return
			}

			console.log("Image dimensions:", imgInfo.width, "x", imgInfo.height)

			// 将图片转换为像素数据
			const pixelData = await convertImageToPixelData(imagePath, imgInfo.width, imgInfo.height)
			if (!pixelData) {
				console.error("Failed to convert image to pixel data")
				detectionStatus.value = 'failed'
				statusText.value = '图片数据转换失败'
				return
			}

			avatar_arrayBuffer.value = pixelData
			console.log("Image converted to pixel data, size:", pixelData.byteLength)

			// 在页面Canvas上显示图片
			await displayImageOnCanvas(imagePath, imgInfo.width, imgInfo.height)

			// 执行人体识别
			const result = await performBodyDetection(pixelData, imgInfo.width, imgInfo.height)
			console.log("Final detection result:", result)

		} catch (error) {
			console.error("formatFaceData error:", error)
			detectionStatus.value = 'failed'
			statusText.value = '检测过程出错: ' + error.message
		}
	}

	// 读取图片文件
	function readImageFile(imagePath) {
		return new Promise((resolve, reject) => {
			const fs = uni.getFileSystemManager()
			fs.readFile({
				filePath: imagePath,
				encoding: '', // 确保以二进制方式读取
				success: (data) => {
					console.log("File read success, data type:", typeof data.data)
					console.log("File data is ArrayBuffer:", data.data instanceof ArrayBuffer)
					console.log("File size:", data.data.byteLength || data.data.length)
					resolve(data.data)
				},
				fail: (err) => {
					console.error("Read file failed:", err)
					reject(err)
				}
			})
		})
	}

	// 获取图片信息
	function getImageInfo(imagePath) {
		return new Promise((resolve, reject) => {
			uni.getImageInfo({
				src: imagePath,
				success: (res) => {
					resolve(res)
				},
				fail: (err) => {
					console.error("Get image info failed:", err)
					reject(err)
				}
			})
		})
	}

	// 将图片转换为像素数据（按照官方示例）
	function convertImageToPixelData(imagePath, width, height) {
		return new Promise(async (resolve, reject) => {
			try {
				// 使用官方示例的方法：createOffscreenCanvas
				const canvas = wx.createOffscreenCanvas({
					type: '2d',
					width: width,
					height: height,
				})
				const context = canvas.getContext('2d')
				const img = canvas.createImage()

				await new Promise(imgResolve => {
					img.onload = imgResolve
					img.src = imagePath
				})

				context.clearRect(0, 0, width, height)
				context.drawImage(img, 0, 0, width, height)

				const imgData = context.getImageData(0, 0, width, height)

				console.log("Pixel data extracted, length:", imgData.data.length)
				console.log("Expected length (width*height*4):", width * height * 4)
				console.log("imgData.data.buffer byteLength:", imgData.data.buffer.byteLength)

				// 按照官方示例，直接使用 imgData.data.buffer
				resolve(imgData.data.buffer)

			} catch (error) {
				console.error("convertImageToPixelData error:", error)
				reject(error)
			}
		})
	}

	// 在页面Canvas上显示图片
	function displayImageOnCanvas(imagePath, width, height) {
		return new Promise((resolve, reject) => {
			const query = wx.createSelectorQuery()
			query.select('#myCanvas')
			  .fields({ node: true, size: true })
			  .exec((res) => {
				if (!res[0] || !res[0].node) {
					reject(new Error("Canvas not found"))
					return
				}

				const canvas = res[0].node
				const ctx = canvas.getContext('2d')

				// 设置canvas尺寸适应显示
				const canvasWidth = res[0].width
				const canvasHeight = res[0].height
				canvas.width = canvasWidth
				canvas.height = canvasHeight

				const img = canvas.createImage()
				img.onload = () => {
					try {
						// 清除canvas
						ctx.clearRect(0, 0, canvasWidth, canvasHeight)

						// 计算图片在canvas中的显示尺寸（保持比例）
						const scale = Math.min(canvasWidth / width, canvasHeight / height)
						const displayWidth = width * scale
						const displayHeight = height * scale
						const x = (canvasWidth - displayWidth) / 2
						const y = (canvasHeight - displayHeight) / 2

						// 绘制图片
						ctx.drawImage(img, x, y, displayWidth, displayHeight)

						console.log("Image displayed on canvas successfully")
						resolve()
					} catch (canvasError) {
						console.error("Canvas display error:", canvasError)
						reject(canvasError)
					}
				}

				img.onerror = (imgError) => {
					console.error("Image load error for display:", imgError)
					reject(new Error("Failed to load image for display"))
				}

				img.src = imagePath
			})
		})
	}

	// 执行人体识别
	function performBodyDetection(frameBuffer, width, height) {
		return new Promise((resolve, reject) => {
			try {
				const session = wx.createVKSession({
					track: {
						body: { mode: 2 } // mode: 2 - 手动传入图像
					},
				})

				// 添加超时处理
				let timeoutId = setTimeout(() => {
					console.log("=== Detection timeout ===")
					console.log("No updateAnchors event received within 5 seconds")
					detectionStatus.value = 'failed'
					statusText.value = '检测超时，可能图片中没有人体或检测失败'
					resolve([])
				}, 5000)

				// 静态图片检测模式下，每调一次 detectBody 接口就会触发一次 updateAnchors 事件
				session.on('updateAnchors', anchors => {
					console.log("=== updateAnchors event triggered ===")
					console.log("Body detection success, anchors count:", anchors.length)
					console.log("Anchors data:", anchors)

					// 清除超时
					clearTimeout(timeoutId)

					// 更新页面状态
					bodyAnchors.value = anchors

					if (anchors.length > 0) {
						detectionStatus.value = 'success'
						statusText.value = `检测成功！发现 ${anchors.length} 个人体`

						anchors.forEach((anchor, index) => {
							console.log(`Anchor ${index}:`, {
								points: anchor.points?.length || 0, // 关键点坐标
								origin: anchor.origin, // 识别框起始点坐标
								size: anchor.size, // 识别框的大小
								score: anchor.score, // 置信度
								confidence: anchor.confidence // 关键点置信度
							})
						})
						handleBodyDetectionResult(anchors)
					} else {
						detectionStatus.value = 'success'
						statusText.value = '检测完成，但未发现人体'
					}

					resolve(anchors)
				})

				// 需要调用一次 start 以启动
				session.start(errno => {
					if (errno) {
						console.log("VK session start error:", errno)
						reject(new Error(`VK session start failed: ${errno}`))
						return
					}

					console.log("VK session started successfully")
					console.log("Detection parameters:", {
						bufferSize: frameBuffer?.byteLength,
						width: width,
						height: height,
						expectedSize: width * height * 4
					})

					try {
						// 按照官方示例调用检测接口
						console.log("Calling detectBody with parameters:", {
							frameBuffer: frameBuffer.byteLength,
							width: width,
							height: height,
							scoreThreshold: 0.5,
							sourceType: 1
						})

						session.detectBody({
							frameBuffer: frameBuffer, // 图片 ArrayBuffer 数据
							width: width, // 图像宽度
							height: height, // 图像高度
							scoreThreshold: 0.5, // 评分阈值，官方推荐0.5
							sourceType: 1 // 图片来源，1表示随机图片
						})

						// resolved: 这里说明调用成功了是吗？怎么看调用返回的信息呢？直观一点让我看
						// 答：detectBody调用成功，但结果通过updateAnchors事件回调获取，现在页面上会直观显示检测结果
						console.log("detectBody called successfully")

					} catch (detectError) {
						console.error("Body detection error:", detectError)
						reject(detectError)
					}
				})

			} catch (sessionError) {
				console.error("VK session creation error:", sessionError)
				reject(sessionError)
			}
		})
	}

	// 处理人体识别结果
	function handleBodyDetectionResult(result) {
		// resolved: 处理识别到的人体关键点数据
		console.log("Processing body detection result...")

		// 这里可以根据识别结果进行后续处理
		// 比如：标记关键点位置、调整头像位置等
		result.forEach((point, index) => {
			console.log(`Body point ${index}:`, point)
		})
	}
	// 生成海报
	function onCreateAvatar() {
		const query = wx.createSelectorQuery()
		query.select('#myCanvas')
		  .fields({ node: true, size: true })
		  .exec((res) => {
			const canvas = res[0].node
			const ctx = canvas.getContext('2d')
	
			const dpr = wx.getWindowInfo().pixelRatio
			const width= res[0].width
			const height = res[0].height
			canvas.width=width*dpr
			canvas.height=height*dpr
			ctx.scale(dpr, dpr)
			const bgImage = canvas.createImage()
			const img2 = canvas.createImage()
			const avatarImg=canvas.createImage()
		
			ctx.imageSmoothingEnabled = true; 
			bgImage.src = '/static/images/<EMAIL>'
			bgImage.crossOrigin = 'anonymous'; 
			bgImage.onerror = function() {
				console.log('图片加载失败，请尝试其他图片');
			};
			bgImage.onload = (e) => {
				ctx.clearRect(0, 0, canvas.width, canvas.height);
				ctx.drawImage(
					bgImage,
					0,
					0,
					width,
					height,
				)
				avatarImg.src='/static/images/image.png'
				avatarImg.crossOrigin = 'anonymous';
				const avatarImgDx=width-30-182
				const avatarImgDy=24
				avatarImg.onload = (e) => {
					ctx.drawImage(bgImage, 0, 0, width,height);
					ctx.drawImage(
					    avatarImg,
						avatarImgDx,
						avatarImgDy,
						182,
						284
					)
					img2.src = "/static/images/<EMAIL>"
					img2.crossOrigin = 'anonymous';
					const img2H=width*107/500
					const img2dy=height-img2H
					img2.onload = (e) => {
						ctx.drawImage(
							img2,
							0,
							img2dy,
							width,
							img2H,
						)
						drawText(ctx)
						uni.canvasToTempFilePath({
						    canvas: canvas,
						    success(res) {
								avatar.value=res.tempFilePath
						    }
						})
					}
				}
			}
		})
		function drawText(ctx){
			 // 绘制名字
			ctx.font = `bold 36px 苹方-简 中粗体`;
			ctx.fillStyle = "#092119";
			ctx.textAlign = "left";
			ctx.textBaseline = 'middle';
			ctx.fillText("林薇", 53, 106);
			// 公司
			ctx.font = `18px 苹方-简 常规体`;
			ctx.fillStyle = "#092119";
			ctx.textAlign = "left";
			ctx.fillText("EaseInsur", 53, 160);
			ctx.fillText("高级销售经理", 53, 195);
		}
	}
	
</script>

<style scoped lang="scss">
	.poster_canvas{
		width: 750rpx;  // 750 *600
		height: 600rpx;
		// position: fixed;
		// top: -10000rpx;
		left: 0rpx;
		box-sizing: border-box;
	}

	.detection-result {
		margin: 20rpx;
		padding: 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		background-color: #f9f9f9;

		.result-title {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
			color: #333;
		}

		.result-status {
			font-size: 28rpx;
			margin-bottom: 15rpx;
			padding: 10rpx;
			border-radius: 5rpx;

			&.waiting {
				background-color: #fff3cd;
				color: #856404;
			}

			&.detecting {
				background-color: #cce5ff;
				color: #004085;
			}

			&.success {
				background-color: #d4edda;
				color: #155724;
			}

			&.failed {
				background-color: #f8d7da;
				color: #721c24;
			}
		}

		.anchors-info {
			.anchor-count {
				font-size: 30rpx;
				font-weight: bold;
				color: #28a745;
				margin-bottom: 15rpx;
			}

			.anchor-item {
				background-color: white;
				padding: 15rpx;
				margin-bottom: 10rpx;
				border-radius: 8rpx;
				border-left: 4rpx solid #28a745;

				.anchor-title {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 8rpx;
				}

				.anchor-detail {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 5rpx;
				}
			}
		}
	}
</style>
