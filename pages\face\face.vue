<template>
	<view class="content">
		<canvas type="2d" id="myCanvas" class="poster_canvas"></canvas>
		<view class="my-base"><button @click="onCreateAvatar">生成头像</button></view>
		<button open-type="share" class="bg-theme-brand text-white py-1 font-12 flex align-center justify-evenly rounded-8 card-box">
			分享名片
		</button>
		<view class="my-base">
			生成后的头像：
		</view>
		<image :src='avatar' mode="heightFix" class="width-100"></image>
	</view>
</template>

<script setup>
	import {ref} from "vue"
	import { onShareAppMessage, onLoad, onReady,onUnload } from '@dcloudio/uni-app';
	const avatar=ref("")  // 头像
	const  avatar_arrayBuffer=ref("") // 头像 数据
	//用户点击分享
	onShareAppMessage((e) => {
		let shareInfo = {
			path: `/pages/index/index?id=10`,
			title: `张三`,
			imageUrl: avatar.value
		};
		return shareInfo;
	});
	onLoad(()=>{
		formatFaceData()
		// onCreateAvatar()
	})
	// 函数
	//  人体识别处理
	async function formatFaceData() {
		try {
			const imagePath = '/static/images/body.png'
			console.log("Starting body detection for:", imagePath)

			// 检查VK API是否可用
			if (typeof wx.createVKSession !== 'function') {
				console.log("VK API not available, skipping body detection")
				return
			}

			console.log("VK API is available, proceeding with detection")

			// 获取图片信息
			const imgInfo = await getImageInfo(imagePath)
			if (!imgInfo) {
				console.error("Failed to get image info")
				return
			}

			console.log("Image dimensions:", imgInfo.width, "x", imgInfo.height)

			// 将图片转换为像素数据
			const pixelData = await convertImageToPixelData(imagePath, imgInfo.width, imgInfo.height)
			if (!pixelData) {
				console.error("Failed to convert image to pixel data")
				return
			}

			avatar_arrayBuffer.value = pixelData
			console.log("Image converted to pixel data, size:", pixelData.byteLength)

			// 执行人体识别
			const result = await performBodyDetection(pixelData, imgInfo.width, imgInfo.height)
			console.log("Final detection result:", result)

		} catch (error) {
			console.error("formatFaceData error:", error)
		}
	}

	// 读取图片文件
	function readImageFile(imagePath) {
		return new Promise((resolve, reject) => {
			const fs = uni.getFileSystemManager()
			fs.readFile({
				filePath: imagePath,
				encoding: '', // 确保以二进制方式读取
				success: (data) => {
					console.log("File read success, data type:", typeof data.data)
					console.log("File data is ArrayBuffer:", data.data instanceof ArrayBuffer)
					console.log("File size:", data.data.byteLength || data.data.length)
					resolve(data.data)
				},
				fail: (err) => {
					console.error("Read file failed:", err)
					reject(err)
				}
			})
		})
	}

	// 获取图片信息
	function getImageInfo(imagePath) {
		return new Promise((resolve, reject) => {
			uni.getImageInfo({
				src: imagePath,
				success: (res) => {
					resolve(res)
				},
				fail: (err) => {
					console.error("Get image info failed:", err)
					reject(err)
				}
			})
		})
	}

	// 将图片转换为像素数据
	function convertImageToPixelData(imagePath, width, height) {
		return new Promise((resolve, reject) => {
			try {
				// 创建临时canvas来获取像素数据
				const canvas = wx.createCanvas()
				const ctx = canvas.getContext('2d')

				canvas.width = width
				canvas.height = height

				const img = canvas.createImage()
				img.onload = () => {
					try {
						// 绘制图片到canvas
						ctx.drawImage(img, 0, 0, width, height)

						// 获取像素数据
						const imageData = ctx.getImageData(0, 0, width, height)
						console.log("Pixel data extracted, length:", imageData.data.length)
						console.log("Expected length (width*height*4):", width * height * 4)

						// imageData.data 是 Uint8ClampedArray，需要转换为 ArrayBuffer
						const arrayBuffer = imageData.data.buffer.slice(
							imageData.data.byteOffset,
							imageData.data.byteOffset + imageData.data.byteLength
						)

						console.log("ArrayBuffer created, byteLength:", arrayBuffer.byteLength)
						resolve(arrayBuffer)
					} catch (canvasError) {
						console.error("Canvas processing error:", canvasError)
						reject(canvasError)
					}
				}

				img.onerror = (imgError) => {
					console.error("Image load error:", imgError)
					reject(new Error("Failed to load image"))
				}

				img.src = imagePath

			} catch (error) {
				console.error("convertImageToPixelData error:", error)
				reject(error)
			}
		})
	}

	// 执行人体识别
	function performBodyDetection(frameBuffer, width, height) {
		return new Promise((resolve, reject) => {
			try {
				const session = wx.createVKSession({
					track: {
						body: { mode: 2 }
					},
				})

				session.on('updateAnchors', anchors => {
					console.log("Body anchors updated:", anchors)
				})

				session.start(errno => {
					if (errno) {
						console.log("VK session start error:", errno)
						reject(new Error(`VK session start failed: ${errno}`))
						return
					}

					console.log("VK session started successfully")
					console.log("Detection parameters:", {
						width: width,
						height: height,
						bufferSize: frameBuffer ? frameBuffer.byteLength : 'null'
					})

					console.log("Detection parameters:", {
						bufferSize: frameBuffer?.byteLength,
						width: width,
						height: height,
						expectedSize: width * height * 4
					})

					try {
						const result = session.detectBody({
							frameBuffer: frameBuffer,
							width: width,
							height: height,
							scoreThreshold: 0.3,
							sourceType: 1,
						})

						console.log("Body detection result:", result)
						console.log("Result type:", typeof result)

						if (result && Array.isArray(result) && result.length > 0) {
							console.log("Body detected successfully, points count:", result.length)
							handleBodyDetectionResult(result)
						} else if (result === undefined || result === null) {
							console.log("Detection returned undefined/null")
							console.log("This usually means:")
							console.log("1. No body detected in image")
							console.log("2. Image data format incorrect")
							console.log("3. Image dimensions mismatch")
						} else {
							console.log("Detection completed, result:", result)
						}

						resolve(result)

					} catch (detectError) {
						console.error("Body detection error:", detectError)
						reject(detectError)
					}
				})

			} catch (sessionError) {
				console.error("VK session creation error:", sessionError)
				reject(sessionError)
			}
		})
	}

	// 处理人体识别结果
	function handleBodyDetectionResult(result) {
		// resolved: 处理识别到的人体关键点数据
		console.log("Processing body detection result...")

		// 这里可以根据识别结果进行后续处理
		// 比如：标记关键点位置、调整头像位置等
		result.forEach((point, index) => {
			console.log(`Body point ${index}:`, point)
		})
	}
	// 生成海报
	function onCreateAvatar() {
		const query = wx.createSelectorQuery()
		query.select('#myCanvas')
		  .fields({ node: true, size: true })
		  .exec((res) => {
			const canvas = res[0].node
			const ctx = canvas.getContext('2d')
	
			const dpr = wx.getSystemInfoSync().pixelRatio
			const width= res[0].width
			const height = res[0].height
			canvas.width=width*dpr
			canvas.height=height*dpr
			ctx.scale(dpr, dpr)
			const bgImage = canvas.createImage()
			const img2 = canvas.createImage()
			const avatarImg=canvas.createImage()
		
			ctx.imageSmoothingEnabled = true; 
			bgImage.src = '/static/images/<EMAIL>'
			bgImage.crossOrigin = 'anonymous'; 
			bgImage.onerror = function() {
				console.log('图片加载失败，请尝试其他图片');
			};
			bgImage.onload = (e) => {
				ctx.clearRect(0, 0, canvas.width, canvas.height);
				ctx.drawImage(
					bgImage,
					0,
					0,
					width,
					height,
				)
				avatarImg.src='/static/images/image.png'
				avatarImg.crossOrigin = 'anonymous';
				const avatarImgDx=width-30-182
				const avatarImgDy=24
				avatarImg.onload = (e) => {
					ctx.drawImage(bgImage, 0, 0, width,height);
					ctx.drawImage(
					    avatarImg,
						avatarImgDx,
						avatarImgDy,
						182,
						284
					)
					img2.src = "/static/images/<EMAIL>"
					img2.crossOrigin = 'anonymous';
					const img2H=width*107/500
					const img2dy=height-img2H
					img2.onload = (e) => {
						ctx.drawImage(
							img2,
							0,
							img2dy,
							width,
							img2H,
						)
						drawText(ctx)
						uni.canvasToTempFilePath({
						    canvas: canvas,
						    success(res) {
								avatar.value=res.tempFilePath
						    }
						})
					}
				}
			}
		})
		function drawText(ctx){
			 // 绘制名字
			ctx.font = `bold 36px 苹方-简 中粗体`;
			ctx.fillStyle = "#092119";
			ctx.textAlign = "left";
			ctx.textBaseline = 'middle';
			ctx.fillText("林薇", 53, 106);
			// 公司
			ctx.font = `18px 苹方-简 常规体`;
			ctx.fillStyle = "#092119";
			ctx.textAlign = "left";
			ctx.fillText("EaseInsur", 53, 160);
			ctx.fillText("高级销售经理", 53, 195);
		}
	}
	
</script>

<style scoped lang="scss">
	.poster_canvas{
		width: 750rpx;  // 750 *600
		height: 600rpx;
		// position: fixed;
		// top: -10000rpx;
		left: 0rpx;
		box-sizing: border-box;
	}
</style>
